const express = require('express');
const { v4: uuidv4 } = require('uuid');
const router = express.Router();

const User = require('../models/User');
const ActivationKey = require('../models/ActivationKey');
const UserActivity = require('../models/UserActivity');
// const { generateToken } = require('../middleware/auth');
const { decryptActivationKey, generateDeviceKey } = require('../utils/encryption');
const logger = require('../utils/logger');

// POST /api/v1/auth/activate - Activate device with admin-generated key
router.post('/activate', async (req, res) => {
  try {
    const { activationKey, deviceInfo } = req.body;

    if (!activationKey || !deviceInfo || !deviceInfo.deviceId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields',
        code: 'MISSING_FIELDS'
      });
    }

    // Decrypt and validate activation key
    const keyData = decryptActivationKey(activationKey);

    if (!keyData.isValid) {
      logger.error('Invalid activation key attempt', {
        deviceId: deviceInfo.deviceId,
        error: keyData.error
      });

      return res.status(400).json({
        success: false,
        error: keyData.error || 'Invalid activation key',
        code: keyData.isExpired ? 'KEY_EXPIRED' : 'INVALID_ACTIVATION_KEY'
      });
    }

    // Verify device ID matches
    if (keyData.deviceId !== deviceInfo.deviceId) {
      logger.error('Device ID mismatch', {
        expectedDeviceId: keyData.deviceId,
        actualDeviceId: deviceInfo.deviceId
      });

      return res.status(400).json({
        success: false,
        error: 'Device ID mismatch',
        code: 'DEVICE_MISMATCH'
      });
    }

    // Check if device is already activated
    const existingUser = await User.findOne({
      'deviceInfo.deviceId': deviceInfo.deviceId
    });

    if (existingUser && existingUser.deviceInfo.isActivated) {
      return res.status(400).json({
        success: false,
        error: 'Device already activated',
        code: 'DEVICE_ALREADY_ACTIVATED'
      });
    }

    // Find the activation key record
    const activationKeyRecord = await ActivationKey.findByActivationKey(activationKey);
    if (!activationKeyRecord) {
      return res.status(400).json({
        success: false,
        error: 'Activation key not found',
        code: 'KEY_NOT_FOUND'
      });
    }

    if (!activationKeyRecord.isValid()) {
      return res.status(400).json({
        success: false,
        error: 'Activation key is no longer valid',
        code: 'KEY_INVALID'
      });
    }

    // Create or update user
    let user;
    if (existingUser) {
      // Update existing user
      user = existingUser;
      user.deviceInfo.isActivated = true;
      user.deviceInfo.activatedAt = new Date();
      user.deviceInfo.deviceModel = deviceInfo.deviceModel;
      user.deviceInfo.osVersion = deviceInfo.osVersion;
      user.deviceInfo.appVersion = deviceInfo.appVersion;
      user.isActive = true;
      await user.save();
    } else {
      // Create new user with data from activation key
      user = new User({
        userId: keyData.userId,
        fullName: `User ${keyData.userId}`, // This should be updated by user later
        email: `${keyData.userId}@temp.nso.gov.ng`, // Temporary email
        role: keyData.role,
        facility: keyData.facility,
        state: 'Nigeria', // Default state
        contactInfo: 'Not provided',
        deviceInfo: {
          deviceId: deviceInfo.deviceId,
          activationKeyId: activationKeyRecord.keyId,
          isActivated: true,
          activatedAt: new Date(),
          deviceModel: deviceInfo.deviceModel,
          osVersion: deviceInfo.osVersion,
          appVersion: deviceInfo.appVersion,
          deviceFingerprint: generateDeviceKey(deviceInfo.deviceId, deviceInfo.deviceModel, deviceInfo.osVersion)
        },
        isActive: true
      });

      await user.save();
    }

    // Mark activation key as used
    await activationKeyRecord.markAsUsed(deviceInfo);

    // Generate JWT token
    const token = generateToken({
      userId: user.userId,
      deviceId: user.deviceInfo.deviceId,
      role: user.role
    });

    // Log successful activation
    logger.info('Device activated successfully', {
      userId: user.userId,
      deviceId: user.deviceInfo.deviceId,
      facility: user.facility,
      keyId: activationKeyRecord.keyId
    });

    // Log user activity
    const activity = new UserActivity({
      activityId: uuidv4(),
      userId: user.userId,
      deviceId: user.deviceInfo.deviceId,
      sessionId: uuidv4(),
      activityType: 'authentication',
      action: {
        name: 'device_activation',
        target: 'auth_system',
        value: 'success'
      },
      deviceContext: deviceInfo,
      timestamp: new Date()
    });

    await activity.save();

    res.status(200).json({
      success: true,
      message: 'Device activated successfully',
      data: {
        user: {
          userId: user.userId,
          fullName: user.fullName,
          role: user.role,
          facility: user.facility,
          deviceId: user.deviceInfo.deviceId
        },
        token,
        expiresIn: '7d',
        keyExpiresAt: activationKeyRecord.expiresAt,
        remainingDays: keyData.remainingDays
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Activation error:', error);

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

// POST /api/v1/auth/login - Login with activated device
router.post('/login', async (req, res) => {
  try {
    const { deviceId, activationKey } = req.body;

    if (!deviceId || !activationKey) {
      return res.status(400).json({
        success: false,
        error: 'Missing device ID or activation key',
        code: 'MISSING_CREDENTIALS'
      });
    }

    // Find user by device ID
    const user = await User.findOne({
      'deviceInfo.deviceId': deviceId,
      isActive: true
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Device not found or inactive',
        code: 'DEVICE_NOT_FOUND'
      });
    }

    if (!user.deviceInfo.isActivated) {
      return res.status(401).json({
        success: false,
        error: 'Device not activated',
        code: 'DEVICE_NOT_ACTIVATED'
      });
    }

    // Verify activation key
    const keyData = decryptActivationKey(activationKey);
    if (!keyData.isValid || keyData.deviceId !== deviceId || keyData.userId !== user.userId) {
      logger.error('Invalid login attempt', {
        userId: user.userId,
        deviceId,
        error: keyData.error
      });

      return res.status(401).json({
        success: false,
        error: 'Invalid credentials',
        code: 'INVALID_CREDENTIALS'
      });
    }

    // Check if key is expired
    if (keyData.isExpired) {
      return res.status(401).json({
        success: false,
        error: 'Activation key has expired',
        code: 'KEY_EXPIRED'
      });
    }

    // Generate new JWT token
    const token = generateToken({
      userId: user.userId,
      deviceId: user.deviceInfo.deviceId,
      role: user.role
    });

    // Update last login
    user.lastLoginAt = new Date();
    await user.save();

    // Log login activity
    const activity = new UserActivity({
      activityId: uuidv4(),
      userId: user.userId,
      deviceId: user.deviceInfo.deviceId,
      sessionId: uuidv4(),
      activityType: 'authentication',
      action: {
        name: 'login',
        target: 'auth_system',
        value: 'success'
      },
      timestamp: new Date()
    });

    await activity.save();

    logger.info('User logged in successfully', {
      userId: user.userId,
      deviceId: user.deviceInfo.deviceId
    });

    res.status(200).json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          userId: user.userId,
          fullName: user.fullName,
          role: user.role,
          facility: user.facility,
          state: user.state,
          deviceId: user.deviceInfo.deviceId,
          lastLoginAt: user.lastLoginAt
        },
        token,
        expiresIn: '7d',
        keyExpiresAt: keyData.expiresAt,
        remainingDays: keyData.remainingDays
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Login error:', error);

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

// POST /api/v1/auth/logout - Logout user
router.post('/logout', async (req, res) => {
  try {
    const deviceId = req.headers['x-device-id'];
    const sessionId = req.headers['x-session-id'];
    
    if (deviceId && sessionId) {
      // Log logout activity
      const user = await User.findOne({ 'deviceInfo.deviceId': deviceId });
      
      if (user) {
        await UserActivity.create({
          activityId: uuidv4(),
          userId: user.userId,
          deviceId,
          sessionId,
          activityType: 'logout',
          action: {
            name: 'user_logout',
            target: 'auth_system',
            value: 'success'
          },
          timestamp: new Date()
        });
        
        logger.logSecurity('USER_LOGOUT', user.userId, { deviceId, sessionId });
      }
    }
    
    res.status(200).json({
      success: true,
      message: 'Logout successful',
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Logout error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during logout',
      code: 'LOGOUT_ERROR'
    });
  }
});

// GET /api/v1/auth/verify - Verify token
router.get('/verify', async (req, res) => {
  try {
    const authHeader = req.header('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: 'No token provided',
        code: 'NO_TOKEN'
      });
    }
    
    const token = authHeader.substring(7);
    const jwt = require('jsonwebtoken');
    const config = require('../config');
    
    try {
      const decoded = jwt.verify(token, config.JWT_SECRET);
      
      const user = await User.findOne({ 
        userId: decoded.userId,
        'deviceInfo.deviceId': decoded.deviceId,
        isActive: true 
      });
      
      if (!user || !user.deviceInfo.isActivated) {
        return res.status(401).json({
          success: false,
          error: 'Invalid token or inactive user',
          code: 'INVALID_TOKEN'
        });
      }
      
      res.status(200).json({
        success: true,
        valid: true,
        user: {
          userId: user.userId,
          fullName: user.fullName,
          role: user.role,
          facility: user.facility,
          deviceId: user.deviceInfo.deviceId
        },
        expiresAt: new Date(decoded.exp * 1000).toISOString(),
        timestamp: new Date().toISOString()
      });
      
    } catch (_jwtError) {
      res.status(401).json({
        success: false,
        valid: false,
        error: 'Invalid or expired token',
        code: 'INVALID_TOKEN'
      });
    }
    
  } catch (error) {
    logger.error('Token verification error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during verification',
      code: 'VERIFICATION_ERROR'
    });
  }
});

module.exports = router;
